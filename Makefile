.PHONY: clean build test release tag all bump-patch bump-minor bump-major commit-and-push docker-build docker-run docker-stop docker-test docker-publish docker-clean

# File paths
version_file = VERSION
current_version = $(shell cat $(version_file))
dev_ver = $(current_version)-SNAPSHOT

ifeq ($(shell uname), Darwin)
    SED_INPLACE = sed -i ''
else
    SED_INPLACE = sed -i
endif

define bump_version
$(eval MAJOR=$(shell echo $(current_version) | cut -d. -f1))
$(eval MINOR=$(shell echo $(current_version) | cut -d. -f2))
$(eval PATCH=$(shell echo $(current_version) | cut -d. -f3))
endef

# Clean target
clean:
	mvn clean

pipe:
	mvn verify -DskipTests
	mvn compile -DskipTests

# Build target (skip tests)
build:
	mvn clean install -DskipTests

test-ref-1:
	@echo "Starting reference 1*1 server..."
	@java -jar ./libs/reference-server-0.2.3.jar -p 5000 -s 1 > /dev/null 2>&1 & \
	echo $$! > ref_server.pid
	sleep 5
	@echo "Running acceptance tests..."
	mvn -Dtest=za.co.wethinkcode.robots.acceptanceTest.LaunchRobotTests test
	mvn -Dtest=za.co.wethinkcode.robots.acceptanceTest.LookRobotTest test
	mvn -Dtest=za.co.wethinkcode.robots.acceptanceTest.RobotStateTest test
	mvn -Dtest=za.co.wethinkcode.robots.acceptanceTest.ForwardRobotTest test
	@echo "Stopping reference server..."
	@kill -9 `cat ref_server.pid` || true
	@rm -f ref_server.pid
	sleep 3

test-ref-2:
	@echo "Starting reference 2*2 server..."
	@java -jar ./libs/reference-server-0.2.3.jar -p 5000 -s 2 > /dev/null 2>&1 & \
	echo $$! > ref_server.pid
	sleep 5
	@echo "Running acceptance tests..."
	mvn -Dtest=za.co.wethinkcode.robots.acceptanceTest.LaunchRobotTest2x2Empty test
	@echo "Stopping reference server..."
	@kill -9 `cat ref_server.pid` || true
	@rm -f ref_server.pid
	sleep 3

test-ref-2-obstacle:
	@echo "Starting reference 2*2 with obstacle server..."
	@java -jar ./libs/reference-server-0.2.3.jar -p 5000 -s 2 -o 0,1 > /dev/null 2>&1 & \
	echo $$! > ref_server.pid
	sleep 5
	@echo "Running acceptance tests..."
	mvn -Dtest=za.co.wethinkcode.robots.acceptanceTest.LaunchRobotTest2x2Obstacle test
	mvn -Dtest=za.co.wethinkcode.robots.acceptanceTest.LookRobotTest2x2 test
	@echo "Stopping reference server..."
	@kill -9 `cat ref_server.pid` || true
	@rm -f ref_server.pid
	sleep 3

test-unit-test:
	@echo "Running Unit test suite..."
	mvn test -Dtest=\!LaunchRobotTests,\!RobotStateTest,\!ForwardRobotTest,\!LookRobotTest,\!LaunchRobotTest2x2Empty,\!LaunchRobotTest2x2Obstacle,\!LookRobotTest2x2
	@echo "Finishing unit testing..."


test-main-1:
	@echo "Starting our 1*1 server in background..."
	mvn compile exec:java -Dexec.mainClass=za.co.wethinkcode.robots.server.Server -Dexec.args="test -s 1 " > /dev/null 2>&1 & \
	echo $$! > our_server.pid
	sleep 5
	@echo "Running acceptance tests..."
	mvn -Dtest=za.co.wethinkcode.robots.acceptanceTest.LaunchRobotTests test
	mvn -Dtest=za.co.wethinkcode.robots.acceptanceTest.LookRobotTest test
	mvn -Dtest=za.co.wethinkcode.robots.acceptanceTest.RobotStateTest test
	mvn -Dtest=za.co.wethinkcode.robots.acceptanceTest.ForwardRobotTest test
	@echo "Stopping our server..."
	@kill -9 `cat our_server.pid` || true
	@rm -f our_server.pid

test-main-2:
	@echo "Starting our 2*2 server in background..."
	mvn compile exec:java -Dexec.mainClass=za.co.wethinkcode.robots.server.Server -Dexec.args="test -s 2 " > /dev/null 2>&1 & \
	echo $$! > our_server.pid
	sleep 5
	@echo "Running acceptance tests..."
	mvn -Dtest=za.co.wethinkcode.robots.acceptanceTest.LaunchRobotTest2x2Empty test
	@echo "Stopping our server..."
	@kill -9 `cat our_server.pid` || true
	@rm -f our_server.pid

test-main-2-obstacle:
	@echo "Starting our 2*2 server with obstacle in background..."
	mvn compile exec:java -Dexec.mainClass=za.co.wethinkcode.robots.server.Server -Dexec.args="test -s 2 -o 0,1" > /dev/null 2>&1 & \
	echo $$! > our_server.pid
	sleep 5
	mvn -Dtest=za.co.wethinkcode.robots.acceptanceTest.LaunchRobotTest2x2Obstacle test
	mvn -Dtest=za.co.wethinkcode.robots.acceptanceTest.LookRobotTest2x2 test
	@echo "Stopping reference server..."
	@echo "Stopping our server..."
	@kill -9 `cat our_server.pid` || true
	@rm -f our_server.pid

test-ref: test-ref-1 test-ref-2 test-ref-2-obstacle

test-main: test-main-1 test-main-2
# Test target
test: test-unit-test test-ref test-main


# Release target
release:
	@echo "Switching to release version..."
	$(SED_INPLACE) "s/<version>$(dev_ver)<\/version>/<version>$(current_version)<\/version>/" pom.xml
	mvn clean package -DskipTests
	@echo "Reverting to snapshot version..."
	$(SED_INPLACE) "s/<version>$(current_version)<\/version>/<version>$(dev_ver)<\/version>/" pom.xml
	@cp target/robot-world-$(current_version)-jar-with-dependencies.jar libs/my-server-$(current_version).jar
	git add libs/my-server-$(current_version).jar

# Git tagging
tag:
	git tag -a release-v$(current_version) -m "Release version $(current_version)"
	git push origin release-v$(current_version)

# All target
bump-patch:
	@$(call bump_version)
	@MAJOR=$(MAJOR) MINOR=$(MINOR) PATCH=$(PATCH) current_version=$(current_version) version_file=$(version_file) SED_INPLACE="$(SED_INPLACE)" bash -c '\
		NEW_VERSION=$$MAJOR.$$MINOR.$$((PATCH + 1)); \
		echo "Bumping patch: $$current_version → $$NEW_VERSION"; \
		echo $$NEW_VERSION > $$version_file; \
		eval "$$SED_INPLACE \"s/<version>$$current_version-SNAPSHOT<\/version>/<version>$$NEW_VERSION-SNAPSHOT<\/version>/\" pom.xml"; \
	'

# Bump minor version
bump-minor:
	@$(call bump_version)
	@MAJOR=$(MAJOR) MINOR=$(MINOR) current_version=$(current_version) version_file=$(version_file) SED_INPLACE="$(SED_INPLACE)" bash -c '\
		NEW_VERSION=$$MAJOR.$$((MINOR + 1)).0; \
		echo "Bumping minor: $$current_version → $$NEW_VERSION"; \
		echo $$NEW_VERSION > $$version_file; \
		eval "$$SED_INPLACE \"s/<version>$$current_version-SNAPSHOT<\/version>/<version>$$NEW_VERSION-SNAPSHOT<\/version>/\" pom.xml"; \
	'

# Bump major version
bump-major:
	@$(call bump_version)
	@MAJOR=$(MAJOR) current_version=$(current_version) version_file=$(version_file) SED_INPLACE="$(SED_INPLACE)" bash -c '\
		NEW_VERSION=$$((MAJOR + 1)).0.0; \
		echo "Bumping major: $$current_version → $$NEW_VERSION"; \
		echo $$NEW_VERSION > $$version_file; \
		eval "$$SED_INPLACE \"s/<version>$$current_version-SNAPSHOT<\/version>/<version>$$NEW_VERSION-SNAPSHOT<\/version>/\" pom.xml"; \
	'

commit-and-push:
	@git add .
	@git commit -m "Bump version to $(current_version)"
	@git push origin main

all: clean test build release

publish-patch: bump-patch all commit-and-push tag

publish-minor: bump-minor all commit-and-push tag

publish-major: bump-major all commit-and-push tag

# Docker targets
docker-build: build
	@echo "Building Docker image..."
	@docker build -t robot-worlds-server:$(current_version) .
	@docker tag robot-worlds-server:$(current_version) robot-worlds-server:latest
	@echo "Docker image built: robot-worlds-server:$(current_version)"

docker-run:
	@echo "Running Docker container on port 5050..."
	@docker run -d --name robot-worlds-server -p 5050:5050 robot-worlds-server:latest
	@echo "Container started. Server available at http://localhost:5050"
	@echo "To stop: make docker-stop"

docker-stop:
	@echo "Stopping Docker container..."
	@docker stop robot-worlds-server || true
	@docker rm robot-worlds-server || true

docker-test:
	@echo "Testing Docker container..."
	@$(MAKE) docker-build
	@docker run -d --name robot-worlds-test -p 5051:5050 robot-worlds-server:$(current_version)
	@sleep 5
	@echo "Running acceptance tests against Docker container on port 5050..."
	@mvn -Dtest=za.co.wethinkcode.robots.acceptanceTest.LaunchRobotTests -Dserver.port=5050 test
	@mvn -Dtest=za.co.wethinkcode.robots.acceptanceTest.LookRobotTest -Dserver.port=5050 test
	@echo "Stopping test container..."
	@docker stop robot-worlds-test 2>/dev/null || true
	@docker rm robot-worlds-test 2>/dev/null || true

docker-publish: docker-build
	@echo "Publishing Docker image to GitLab Container Registry..."
	@docker tag robot-worlds-server:$(current_version) registry.gitlab.wethinkco.de/jaarnoljhb024/brownfields_robot_worlds_3:$(current_version)
	@docker tag robot-worlds-server:$(current_version) registry.gitlab.wethinkco.de/jaarnoljhb024/brownfields_robot_worlds_3:latest
	@docker push registry.gitlab.wethinkco.de/jaarnoljhb024/brownfields_robot_worlds_3:$(current_version)
	@docker push registry.gitlab.wethinkco.de/jaarnoljhb024/brownfields_robot_worlds_3:latest
	@echo "Docker image published to GitLab Container Registry"

docker-clean:
	@echo "Cleaning up Docker images and containers..."
	@docker stop robot-worlds-server robot-worlds-test 2>/dev/null || true
	@docker rm robot-worlds-server robot-worlds-test 2>/dev/null || true
	@docker rmi robot-worlds-server:$(current_version) robot-worlds-server:latest 2>/dev/null || true
	@echo "Docker cleanup completed"
