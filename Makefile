
.PHONY: clean build test test-iteration1 test-iteration2 test-unittest test_iteration1 test_iteration2 test_unittest release tag all bump-patch bump-minor bump-major commit-and-push publish-patch publish-minor publish-major docker-build docker-run docker-stop docker-test docker-publish docker-clean

# Detect OS
UNAME_S := $(shell uname -s 2>/dev/null || echo Windows)
ifeq ($(UNAME_S),Linux)
    OS = linux
else ifeq ($(UNAME_S),Darwin)
    OS = macos
else
    OS = windows
endif

# File Paths
version_file = VERSION

# Ensure VERSION file exists with default value
$(shell if [ ! -f $(version_file) ]; then echo "1.0.0" > $(version_file); fi)

current_version = $(shell cat $(version_file) | tr -d ' ')
dev_version = $(current_version)-SNAPSHOT

define bump_version
$(eval MAJOR = $(shell echo $(current_version) | cut -d '.' -f 1))
$(eval MINOR = $(shell echo $(current_version) | cut -d '.' -f 2))
$(eval PATCH = $(shell echo $(current_version) | cut -d '.' -f 3))
endef

# Clean
clean:
	@echo "Cleaning project..."
	@mvn clean

# Build
build:
	@echo "Building project..."
	@mvn clean install -DskipTests

# Test
test:
	@echo "Running all tests (iteration1, iteration2, unittest)..."
	@$(MAKE) test-iteration1
	@$(MAKE) test-iteration2
	@$(MAKE) test-unittest

test-iteration1:
	@echo "Cleaning up any existing servers..."
	@if [ "$(OS)" = "windows" ]; then \
		taskkill //f //im java.exe 2>/dev/null || true; \
		sleep 2; \
		echo "Starting reference server with 1x1 world..."; \
		start //b java -jar ./libs/reference-server-0.2.3.jar -p 5000 -s 1; \
		sleep 5; \
	else \
		pkill -f "reference-server" 2>/dev/null || true; \
		sleep 2; \
		echo "Starting reference server with 1x1 world..."; \
		java -jar ./libs/reference-server-0.2.3.jar -p 5000 -s 1 & \
		sleep 5; \
	fi
	@echo "Running acceptance tests..."
	@mvn -Dtest=acceptance_test.LaunchAcceptanceTest test
	@mvn -Dtest=acceptance_test.LookAcceptanceTest test
	@mvn -Dtest=acceptance_test.WorldTests.WorldRobotDeathPitAcceptanceTest test
	@mvn -Dtest=acceptance_test.WorldTests.WorldInitializationAcceptanceTest test
	@mvn -Dtest=acceptance_test.WorldTests.WorldPositionValidationAcceptanceTest test
	@mvn -Dtest=acceptance_test.WorldTests.WorldRobotAdditionAcceptanceTest test
	@echo "All tests completed. Now killing reference server..."
	@if [ "$(OS)" = "windows" ]; then \
		taskkill //f //im java.exe //fi "WINDOWTITLE eq reference-server*" 2>/dev/null || true; \
		taskkill //f //im java.exe //fi "COMMANDLINE eq *reference-server*" 2>/dev/null || true; \
	else \
		pkill -f "reference-server" 2>/dev/null || true; \
	fi

test_iteration1: test-iteration1

test-iteration2:
	@echo "Cleaning up any existing servers..."
	@if [ "$(OS)" = "windows" ]; then \
		taskkill //f //im java.exe 2>/dev/null || true; \
		sleep 2; \
		echo "Starting reference server with 2x2 world..."; \
		start //b java -jar ./libs/reference-server-0.2.3.jar -p 5000 -s 2 -o 1,1; \
		sleep 5; \
	else \
		pkill -f "reference-server" 2>/dev/null || true; \
		sleep 2; \
		echo "Starting reference server with 2x2 world..."; \
		java -jar ./libs/reference-server-0.2.3.jar -p 5000 -s 2 -o 1,1 & \
		sleep 5; \
	fi
	@echo "Running acceptance tests..."
	@mvn -Dtest=acceptance_test.LaunchRobotAcceptanceTest test
	@mvn -Dtest=acceptance_test.Moveforward.MoveForwardAcceptanceTest test
	@mvn -Dtest=acceptance_test.Moveforward.MoveForwardEdgeAcceptanceTest test
	@echo "Killing reference server..."
	@if [ "$(OS)" = "windows" ]; then \
		taskkill //f //im java.exe //fi "WINDOWTITLE eq reference-server*" 2>/dev/null || true; \
		taskkill //f //im java.exe //fi "COMMANDLINE eq *reference-server*" 2>/dev/null || true; \
	else \
		pkill -f "reference-server" 2>/dev/null || true; \
	fi

test_iteration2: test-iteration2

test-unittest:
	@echo "Cleaning up any existing servers..."
	@if [ "$(OS)" = "windows" ]; then \
		taskkill //f //im java.exe 2>/dev/null || true; \
		sleep 3; \
		echo "Starting My codebase server..."; \
		start //b mvn exec:java -Dexec.mainClass=za.co.wethinkcode.robots.server.MultiServers -Dexec.args="-p 5000 -s 2"; \
		sleep 5; \
		echo "Running all tests..."; \
		echo "Starting tests with 60 second timeout protection..."; \
		start //b cmd //c "timeout 60 //nobreak >nul && echo Timeout reached, killing processes... && taskkill //f //im java.exe 2>nul && taskkill //f //im cmd.exe //fi \"WINDOWTITLE eq *mvn*\" 2>nul"; \
		mvn test; \
		echo "Killing My codebase server..."; \
		echo "timeout thread interrupted, normal shutdown"; \
		sleep 2; \
		taskkill //f //im java.exe //fi "COMMANDLINE eq *MultiServers*" 2>/dev/null || true; \
		taskkill //f //im java.exe //fi "COMMANDLINE eq *maven*" 2>/dev/null || true; \
	else \
		pkill -f "MultiServers" 2>/dev/null || true; \
		sleep 3; \
		echo "Starting My codebase server..."; \
		nohup mvn exec:java -Dexec.mainClass=za.co.wethinkcode.robots.server.MultiServers -Dexec.args="-p 5000 -s 2" > server.log 2>&1 & \
		echo $$! > server.pid; \
		sleep 5; \
		echo "Running all tests..."; \
		mvn test; \
		echo "Killing My codebase server..."; \
		echo "timeout thread interrupted, normal shutdown"; \
		sleep 2; \
		if [ -f server.pid ]; then kill `cat server.pid` 2>/dev/null || true; rm -f server.pid; fi; \
		pkill -f "MultiServers" 2>/dev/null || true; \
	fi

test_unittest: test-unittest

# Release
release:
	@echo "Switch to release version..."
	@powershell -Command "(Get-Content pom.xml) -replace '<version>$(dev_version)</version>', '<version>$(current_version)</version>' | Set-Content pom.xml"
	@mvn clean package -DskipTests
	@echo "Switch back to new version..."
	@powershell -Command "(Get-Content pom.xml) -replace '<version>$(current_version)</version>', '<version>$(dev_version)</version>' | Set-Content pom.xml"
	@copy target\\robot-world-$(current_version)-jar-with-dependencies.jar libs\\my-server-$(current_version).jar
	@git add libs\\my-server-$(current_version).jar

# Tag
tag:
	@echo "Tagging release..."
	@git tag -a v$(current_version) -m "Release v$(current_version)"
	@git push origin v$(current_version)

# Bump Patch
bump-patch:
	@$(call bump_version)
	@bash -c '\
		MAJOR=$(MAJOR); \
		MINOR=$(MINOR); \
		PATCH=$(PATCH); \
		NEW_VERSION=$$MAJOR.$$MINOR.$$((PATCH + 1)); \
		echo "Bumping patch: $(current_version) → $$NEW_VERSION"; \
		echo $$NEW_VERSION > $(version_file); \
		powershell -Command "(Get-Content pom.xml) -replace \"<version>$(current_version)-SNAPSHOT</version>\", \"<version>$$NEW_VERSION-SNAPSHOT</version>\" | Set-Content pom.xml" \
	'

# Bump Minor
bump-minor:
	@$(call bump_version)
	@bash -c '\
		MAJOR=$(MAJOR); \
		MINOR=$(MINOR); \
		NEW_VERSION=$$MAJOR.$$((MINOR + 1)).0; \
		echo "Bumping minor: $(current_version) → $$NEW_VERSION"; \
		echo $$NEW_VERSION > $(version_file); \
		powershell -Command "(Get-Content pom.xml) -replace \"<version>$(current_version)-SNAPSHOT</version>\", \"<version>$$NEW_VERSION-SNAPSHOT</version>\" | Set-Content pom.xml" \
	'

# Bump Major
bump-major:
	@$(call bump_version)
	@bash -c '\
		MAJOR=$(MAJOR); \
		NEW_VERSION=$$((MAJOR + 1)).0.0; \
		echo "Bumping major: $(current_version) → $$NEW_VERSION"; \
		echo $$NEW_VERSION > $(version_file); \
		powershell -Command "(Get-Content pom.xml) -replace \"<version>$(current_version)-SNAPSHOT</version>\", \"<version>$$NEW_VERSION-SNAPSHOT</version>\" | Set-Content pom.xml" \
	'

# Commit and Push
commit-and-push:
	@echo "Committing changes..."
	@git add .
	@git commit -m "Bump version to $(current_version)"
	@echo "Pushing changes..."
	@git push

all:
	@$(MAKE) clean
	@$(MAKE) test_iteration1
	@$(MAKE) test_iteration2
	@$(MAKE) test_unittest
	@$(MAKE) build
	@$(MAKE) release

publish-patch:
	@$(MAKE) bump-patch
	@$(MAKE) release
	@$(MAKE) tag
	@$(MAKE) commit-and-push

publish-minor:
	@$(MAKE) bump-minor
	@$(MAKE) release
	@$(MAKE) tag
	@$(MAKE) commit-and-push

publish-major:
	@$(MAKE) bump-major
	@$(MAKE) release
	@$(MAKE) tag
	@$(MAKE) commit-and-push

# Docker targets
docker-build: build
	@echo "Building Docker image..."
	@docker build -t robot-worlds-server:$(current_version) .
	@docker tag robot-worlds-server:$(current_version) robot-worlds-server:latest
	@echo "Docker image built: robot-worlds-server:$(current_version)"

docker-run:
	@echo "Running Docker container on port 5050..."
	@docker run -d --name robot-worlds-server -p 5050:5050 robot-worlds-server:latest
	@echo "Container started. Server available at http://localhost:5050"
	@echo "To stop: make docker-stop"

docker-stop:
	@echo "Stopping Docker container..."
	@docker stop robot-worlds-server || true
	@docker rm robot-worlds-server || true

docker-test:
	@echo "Testing Docker container..."
	@$(MAKE) docker-build
	@docker run -d --name robot-worlds-test -p 5051:5050 robot-worlds-server:$(current_version)
	@sleep 5
	@echo "Running acceptance tests against Docker container on port 5050..."
	@mvn -Dtest=acceptance_test.LaunchAcceptanceTest -Dserver.port=5050 test
	@mvn -Dtest=acceptance_test.LookAcceptanceTest -Dserver.port=5050 test
	@echo "Stopping test container..."
	@docker stop robot-worlds-test 2>/dev/null || true
	@docker rm robot-worlds-test 2>/dev/null || true

docker-publish: docker-build
	@echo "Publishing Docker image to GitLab Container Registry..."
	@docker tag robot-worlds-server:$(current_version) registry.gitlab.wethinkco.de/jaarnoljhb024/brownfields_robot_worlds_3:$(current_version)
	@docker tag robot-worlds-server:$(current_version) registry.gitlab.wethinkco.de/jaarnoljhb024/brownfields_robot_worlds_3:latest
	@docker push registry.gitlab.wethinkco.de/jaarnoljhb024/brownfields_robot_worlds_3:$(current_version)
	@docker push registry.gitlab.wethinkco.de/jaarnoljhb024/brownfields_robot_worlds_3:latest
	@echo "Docker image published to GitLab Container Registry"

docker-clean:
	@echo "Cleaning up Docker images and containers..."
	@docker stop robot-worlds-server robot-worlds-test 2>/dev/null || true
	@docker rm robot-worlds-server robot-worlds-test 2>/dev/null || true
	@docker rmi robot-worlds-server:$(current_version) robot-worlds-server:latest 2>/dev/null || true
	@echo "Docker cleanup completed"